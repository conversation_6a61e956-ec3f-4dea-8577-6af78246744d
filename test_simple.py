import pickle; import sys; sys.path.append("."); from src.utils.util import StandardScaler, DummyScaler; print("开始测试..."); with open("./small_test_data/scaler.pkl", "rb") as f: scaler_data = pickle.load(f); print(f"加载成功! scaler_data类型: {type(scaler_data)}"); print(f"scaler_data 键: {list(scaler_data.keys())}"); print("使用StandardScaler加载..."); scaler = StandardScaler(scaler_path="./small_test_data/scaler.pkl", device="cpu"); print("StandardScaler加载成功!")
