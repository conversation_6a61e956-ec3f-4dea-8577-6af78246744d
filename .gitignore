# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a PyInstaller script; this is just in case.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
# According to Python official .gitignore template:
# https://github.com/github/gitignore/blob/master/Python.gitignore
Pipfile.lock

# PEP 582; remove FALSEproject_name PEP and amend this comment
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath files
.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Wandb local files
wandb/

# Project specific - data and outputs  
processed_tec_data/
logs/
eval_results/
outputs/
*.npz
*.pkl
*.hdf5

# 大型模型文件
src/models/tecGPT/gpt2/pytorch_model.bin 
src/models/tecGPT/gpt2/merges.txt
src/models/tecGPT/gpt2/vocab.json

# Optuna database files
*.db
tecgpt_optuna.db

# Memory mapped files and temporary files
*.mmap
Y_temp.mmap

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db 