# conf/sweeper/basic_optuna.yaml
# Optuna sweeper configuration for Hydra
# To run Optuna sweep: python your_script.py -m model=tecGPT trainer.epochs=10 ...
# -m flag enables multirun
# You can override parameters for the sweep directly from the command line.

# Sweeper class
_target_: hydra_plugins.hydra_optuna_sweeper.optuna_sweeper.OptunaSweeper
# For details on Optuna Sweeper configuration, see:
# https://hydra.cc/docs/plugins/optuna_sweeper/

# storage for Optuna study (e.g., "sqlite:///example.db")
# defaults to in-memory if not specified
storage: ${optuna_storage} # 从主配置引用
study_name: ${optuna_study_name} # 从主配置引用

# number of parallel workers
n_jobs: 1

# 'minimize' or 'maximize'
direction: minimize

# number of trials in the study
n_trials: ${optuna_trials} # 从主配置引用

# Optuna Sampler configuration
sampler:
  _target_: optuna.samplers.TPESampler
  seed: ${seed} # 使用全局种子
  # n_startup_trials: 5 # TPE采样器参数
  # multivariate: true # TPE采样器参数

# Optuna Pruner configuration (optional)
# pruner:
#   _target_: optuna.pruners.MedianPruner
#   n_startup_trials: 5
#   n_warmup_steps: 0
#   interval_steps: 1

# Parameters to optimize
params:
  trainer.learning_rate: tag(log, interval(1e-5, 1e-3))
  trainer.batch_size: choice(8, 12, 16, 32, 48)
  model.d_embed: choice(128, 192, 256)
  model.llm_layers_to_use: range(4, 8)
  model.U_unfrozen_mha: range(2, 4)
  trainer.optimizer_type: choice(AdamW, Ranger)