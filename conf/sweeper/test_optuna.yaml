# conf/sweeper/test_optuna.yaml
# 简化的Optuna sweeper配置用于测试
# 使用更小的参数范围以便快速测试和减少内存使用

# Sweeper class
sweeper:
  _target_: hydra_plugins.hydra_optuna_sweeper.OptunaSweeper
  
  # storage for Optuna study
  storage: ${optuna_storage} # 从主配置引用，可以是null（内存）或数据库路径
  study_name: ${optuna_study_name} # 从主配置引用

  # number of parallel workers
  n_jobs: 1

  # 'minimize' or 'maximize'
  direction: minimize

  # number of trials in the study
  n_trials: ${optuna_trials} # 从主配置引用

  # Optuna Sampler configuration
  sampler:
    _target_: optuna.samplers.TPESampler
    seed: ${seed} # 使用全局种子

# 简化的参数优化范围（适合小数据集测试）
params:
  trainer.learning_rate: "loguniform(5e-5, 2e-4)"     # 缩小学习率范围
  trainer.batch_size: "categorical(1, 2)"             # 非常小的批次大小
  model.d_embed: "categorical(64, 128)"               # 简化嵌入维度选择
  model.llm_layers_to_use: "int(2, 3)"               # 只使用2-3层以节省内存
  model.U_unfrozen_mha: "categorical(1, 2)"          # 简化MHA选择
  trainer.optimizer_type: "categorical('AdamW')"      # 只使用AdamW 