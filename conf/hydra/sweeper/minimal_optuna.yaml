# @package hydra/sweeper
_target_: hydra_plugins.hydra_optuna_sweeper.optuna_sweeper.OptunaSweeper

# storage for Optuna study
storage: null  # 使用内存存储
study_name: minimal_test

# number of parallel workers
n_jobs: 1

# 'minimize' or 'maximize'
direction: minimize

# number of trials in the study
n_trials: 2

# Optuna Sampler configuration
sampler:
  _target_: optuna.samplers.TPESampler
  seed: 42

# 最小参数优化
params:
  trainer.learning_rate: tag(log, interval(1e-4, 1e-3))
  trainer.batch_size: choice(2, 4)
