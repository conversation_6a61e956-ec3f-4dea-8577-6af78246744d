# conf/dataset/tec_data_basic.yaml
# 基础数据集配置模板，用于预处理时定义数据划分参数
data_dir: "./processed_tec_data"  # 基础数据目录
scaler_path_template: "{data_version_dir}/scaler.pkl"  # 缩放器路径模板

num_nodes: 2911
n_lat: 41
n_lon: 71
history_len: 36  # P
forecast_len: 12 # S

# 特征维度
tec_feat_dim: 1    # TEC特征维度
sw_feat_dim: 5     # 空间天气特征数
time_feat_dim: 6   # 时间特征数（当启用时）

# 数据集划分（精确到年-月）
train_start_year: 2013
train_start_month: 1
train_end_year: 2021
train_end_month: 12

val_start_year: 2022
val_start_month: 1
val_end_year: 2023
val_end_month: 6

test_start_year: 2023
test_start_month: 7
test_end_year: 2025
test_end_month: 4

# 时间特征控制
use_time_features: false

# 数据版本标识符生成参数
version_id_template: "tr{train_start_year_short}-{train_end_year_short}_v{val_start_year_short}-{val_end_year_short}_t{test_start_year_short}-{test_end_year_short}_h{history_len}_f{forecast_len}_{time_feat_flag}" 